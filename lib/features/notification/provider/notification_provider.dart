import 'package:kitemite_app/model/response/notification/notification_model.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

import '../../../core/failure/app_failure.dart';
import '../../../core/repository/notification/notification_repository.dart';
import 'notification_state.dart';

part 'notification_provider.g.dart';

@Riverpod(keepAlive: true)
class NotificationNotifier extends _$NotificationNotifier {
  late final NotificationRepository repo;

  @override
  NotificationState build() {
    repo = ref.read(getNotificationRepositoryProvider);
    return const NotificationState();
  }

  Future<void> init() async {
    await fetchUnseenCount();
  }

  Future<void> fetchNotifications({int? page, int? perPage}) async {
    state = state.copyWith(isLoading: true, error: null);
    try {
      final response = await repo.getNotifications(
        page: page,
        perPage: 200,
      );

      // Convert API response to our model
      final List<NotificationModel> notifications = [];
      for (final item in response.data ?? []) {
        if (item is NotificationModel) {
          notifications.add(item);
        }
      }

      state = state.copyWith(
        notifications: notifications,
        isLoading: false,
        error: null,
      );

      // Refresh unseen count after fetching notifications
      await fetchUnseenCount();
    } on AppFailure catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: e.message,
      );
    }
  }

  Future<void> markAsRead(int notificationId) async {
    try {
      await repo.markAsRead(notificationId);

      final notifications = [...state.notifications];
      final index = notifications.indexWhere((n) {
        final id = n.id;
        return id != null && id == notificationId.toString();
      });

      if (index != -1) {
        // Create a new notification with updated pivot status
        final updatedNotification = notifications[index].copyWith(
          pivot: notifications[index].pivot?.copyWith(status: 1),
        );
        notifications[index] = updatedNotification;
        state = state.copyWith(notifications: notifications);
      }

      // Refresh unseen count after marking as read
      await fetchUnseenCount();
    } on AppFailure catch (e) {
      state = state.copyWith(error: e.message);
    }
  }

  Future<void> fetchNotificationDetail(int notificationId) async {
    state = state.copyWith(isLoadingDetail: true);
    try {
      final response = await repo.getNotificationDetail(notificationId);
      state = state.copyWith(
        detail: response.data,
        isLoadingDetail: false,
      );

      // Mark notification as read when viewing details
      await markAsRead(notificationId);
    } on AppFailure catch (e) {
      state = state.copyWith(
        error: e.message,
        isLoadingDetail: false,
      );
    }
  }

  Future<void> fetchUnseenCount() async {
    try {
      final count = await repo.getUnseenCount();
      state = state.copyWith(
        unseenCount: count,
      );
    } on AppFailure catch (e) {
      if (e.code != 401) {
        state = state.copyWith(
          error: e.message,
        );
      }
    }
  }
}
